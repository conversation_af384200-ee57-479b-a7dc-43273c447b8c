use sea_orm::*;
use redis::AsyncCommands;
use anyhow::Result;
use crate::models::*;

pub struct QuestionService {
    db: DatabaseConnection,
    redis: redis::Client,
}

impl QuestionService {
    pub fn new(db: DatabaseConnection, redis: redis::Client) -> Self {
        Self { db, redis }
    }

    // 获取题目数据 - 对应 timu() 方法
    pub async fn get_questions(&self, params: QuestionQueryParams) -> Result<PaginatedResponse<QuestionResponse>> {
        let per = params.per.unwrap_or(10);
        let page = params.page.unwrap_or(1);
        let id = params.id.unwrap_or(0);
        let mut biao = params.biao.unwrap_or_else(|| "fbsy".to_string());
        let z = params.z.unwrap_or(0);
        let b = params.b.unwrap_or(0);
        let t = params.t.unwrap_or(0);
        let gen = params.gen.unwrap_or(0);
        let f = params.f.unwrap_or(0);
        let o = params.o.unwrap_or(0);
        let kjid = params.kjid.clone();
        let ids = params.ids.clone();
        let q = params.q.clone();
        let parentid = params.parentid;
        let fast_mode = params.fast.as_deref() == Some("1") || params.fast.as_deref() == Some("true");

        // 构建基础查询条件 - 先设置默认值，后面会根据ids参数覆盖
        let mut allcateid = "1=1".to_string();

        let mut rk = "1 = 1".to_string();

        // 处理 z 参数（资料分析模式）
        if z == 1 {
            if id == 656604 {
                biao = "fbsyzlfx".to_string();
            } else if id == 48644 {
                biao = "fbgwyzlfx".to_string();
            }

            // 构建查询
            let sql_query = if gen == 1 && kjid.is_some() {
                let kjid_str = kjid.as_ref().unwrap();
                let exam_query = format!("SELECT * FROM fbkaojuan WHERE sid = '{}'", kjid_str);

                // 先获取考试卷信息
                if let Ok(Some(exam_row)) = self.db.query_one(
                    Statement::from_string(self.db.get_database_backend(), exam_query)
                ).await {
                    if let Ok(question_ids) = exam_row.try_get::<String>("", "questionIds") {
                        format!(
                            "SELECT * FROM {} WHERE id IN ({}) ORDER BY FIELD(id, {})",
                            biao, question_ids, question_ids
                        )
                    } else {
                        format!("SELECT * FROM {} WHERE 1=0", biao)
                    }
                } else {
                    format!("SELECT * FROM {} WHERE 1=0", biao)
                }
            } else if fast_mode && page > 1000 {
                let offset = (page - 1) * per;
                format!(
                    "SELECT * FROM {} WHERE id <= (SELECT id FROM {} WHERE allcateid LIKE '%{}%' ORDER BY id DESC LIMIT 1 OFFSET {}) AND allcateid LIKE '%{}%' ORDER BY id DESC LIMIT {}",
                    biao, biao, id, offset, id, per
                )
            } else {
                let offset = (page - 1) * per;
                format!(
                    "SELECT * FROM {} WHERE allcateid LIKE '%{}%' ORDER BY id DESC LIMIT {} OFFSET {}",
                    biao, id, per, offset
                )
            };

            // 执行查询并返回
            return self.execute_query_and_return(sql_query, biao, fast_mode, page, per, id, kjid).await;
        }

        // 处理错题模式
        let cuo = if b == 1 {
            "answer != choice".to_string()
        } else {
            "1 = 1".to_string()
        };

        // 处理排序
        let orderby = if o == 1 { "asc" } else { "desc" };
        let ord = match f {
            1 => "correctRatio",
            2 => "sort",
            _ => "id"
        };

        // 处理搜索
        if let Some(q_str) = &q {
            rk = format!(
                "(content LIKE '%{}%' OR answerone LIKE '%{}%' OR answertwo LIKE '%{}%' OR answerthree LIKE '%{}%' OR answerfour LIKE '%{}%')",
                q_str, q_str, q_str, q_str, q_str
            );
        }

        // 处理指定ID列表 - 当有ids时，优先使用ids而不是allcateid
        let mut offset = (page - 1) * per;
        if let Some(ids_str) = &ids {
            allcateid = format!("id IN ({})", ids_str);
            // 🔥 关键修复：当ids只有一个时，重置offset为0（与EggJS一致）
            if ids_str.split(',').count() == 1 {
                offset = 0;
            }
        } else if id > 0 {
            allcateid = format!("allcateid LIKE '%{}%'", id);
        } else {
            allcateid = "1=1".to_string();
        }

        // 处理父级ID
        if let Some(parent_id) = parentid {
            allcateid = format!("parentid IN ({})", parent_id);
        }

        // 构建最终查询
        let sql_query = if t == 1 {
            format!(
                "SELECT * FROM fbsy WHERE allcateid LIKE '%656602%' AND allcateid NOT LIKE '%796885%' AND allcateid NOT LIKE '%796962%' AND allcateid NOT LIKE '%796963%' AND allcateid NOT LIKE '%796964%' AND allcateid NOT LIKE '%796965%' ORDER BY source DESC LIMIT {} OFFSET {}",
                per, offset
            )
        } else if gen == 1 && kjid.is_some() {
            let kjid_str = kjid.as_ref().unwrap();
            let exam_query = format!("SELECT * FROM fbkaojuan WHERE sid = '{}'", kjid_str);

            if let Ok(Some(exam_row)) = self.db.query_one(
                Statement::from_string(self.db.get_database_backend(), exam_query)
            ).await {
                if let Ok(question_ids) = exam_row.try_get::<String>("", "questionIds") {
                    format!(
                        "SELECT * FROM {} WHERE id IN ({}) ORDER BY FIELD(id, {})",
                        biao, question_ids, question_ids
                    )
                } else {
                    format!("SELECT * FROM {} WHERE 1=0", biao)
                }
            } else {
                format!("SELECT * FROM {} WHERE 1=0", biao)
            }
        } else if fast_mode && page > 1000 && ord == "id" && orderby == "desc" {
            format!(
                "SELECT * FROM {} WHERE id <= (SELECT id FROM {} WHERE {} AND {} AND {} ORDER BY {} {} LIMIT 1 OFFSET {}) AND {} AND {} AND {} ORDER BY {} {} LIMIT {}",
                biao, biao, cuo, allcateid, rk, ord, orderby, offset, cuo, allcateid, rk, ord, orderby, per
            )
        } else {
            format!(
                "SELECT * FROM {} WHERE {} AND {} AND {} ORDER BY {} {} LIMIT {} OFFSET {}",
                biao, cuo, allcateid, rk, ord, orderby, per, offset
            )
        };

        // 执行查询并返回结果
        self.execute_query_and_return(sql_query, biao, fast_mode, page, per, id, kjid).await
    }

    // 辅助方法：执行查询并返回结果
    async fn execute_query_and_return(
        &self,
        sql_query: String,
        biao: String,
        fast_mode: bool,
        page: u32,
        per: u32,
        id: i32,
        kjid: Option<String>,
    ) -> Result<PaginatedResponse<QuestionResponse>> {
        tracing::info!("🔍 执行SQL查询: {}", &sql_query[..std::cmp::min(100, sql_query.len())]);

        // 执行主查询
        let questions = Entity::find()
            .from_raw_sql(Statement::from_string(
                self.db.get_database_backend(),
                sql_query,
            ))
            .into_model::<Model>()
            .all(&self.db)
            .await?;

        tracing::info!("🔍 主查询完成，返回 {} 条记录", questions.len());

        // 转换为响应格式，添加题目编号
        let responses: Vec<QuestionResponse> = questions
            .into_iter()
            .enumerate()
            .map(|(index, q)| {
                let mut response = QuestionResponse {
                    id: q.id,
                    content: q.content,
                    ds: q.ds,
                    choice: q.choice,
                    answerone: q.answerone,
                    answertwo: q.answertwo,
                    answerthree: q.answerthree,
                    answerfour: q.answerfour,
                    answer: q.answer,
                    solution: q.solution,
                    source: q.source,
                    created_time: q.created_time,
                    allcateid: q.allcateid,
                    tag: q.tag,
                    correct_ratio: q.correct_ratio,
                    most_wrong_answer: q.most_wrong_answer,
                    material_keys: q.material_keys,
                    parentid: q.parentid,
                    material: q.material,
                    extra: q.extra,
                    canvas_data: q.canvas_data,
                    canvas_width: q.canvas_width,
                    canvas_height: q.canvas_height,
                    has_canvas: q.has_canvas,
                    canvas_updated_time: q.canvas_updated_time,
                };

                // 🔥 计算正确的题目编号（与EggJS完全一致）
                let question_number = if kjid.is_some() {
                    // 新模式：kjid存在时，题目编号就是当前页码
                    page
                } else {
                    // 传统模式：分页模式，计算实际编号
                    (page - 1) * per + index as u32 + 1
                };

                // 只在需要时进行字符串替换
                if let Some(ref content) = response.content {
                    if content.contains('>') {
                        // 与EggJS保持一致：只替换第一个>符号，并添加正确率信息
                        if let Some(pos) = content.find('>') {
                            let mut new_content = content.clone();
                            let correct_ratio = q.correct_ratio.map(|r| r.round() as i32).unwrap_or(0);
                            new_content.replace_range(pos..pos+1, &format!(">{}.({}%)", question_number, correct_ratio));
                            response.content = Some(new_content);
                        }
                    }
                }

                // 🔧 使用统一的选项格式化函数（与EggJS一致）
                if let Some(ref answerone) = response.answerone {
                    response.answerone = Some(format!("A.{}", answerone));
                }
                if let Some(ref answertwo) = response.answertwo {
                    response.answertwo = Some(format!("B.{}", answertwo));
                }
                if let Some(ref answerthree) = response.answerthree {
                    response.answerthree = Some(format!("C.{}", answerthree));
                }
                if let Some(ref answerfour) = response.answerfour {
                    response.answerfour = Some(format!("D.{}", answerfour));
                }

                response
            })
            .collect();

        // 获取总数
        let total = if fast_mode {
            // 快速模式使用估算值
            std::cmp::max(page * per, 100000) as u64
        } else {
            let count_sql = if id > 0 {
                format!("SELECT COUNT(id) as total FROM {} WHERE allcateid LIKE '%{}%'", biao, id)
            } else {
                format!("SELECT COUNT(id) as total FROM {}", biao)
            };

            let count_result = self.db.query_one(
                Statement::from_string(self.db.get_database_backend(), count_sql)
            ).await?;

            count_result
                .and_then(|row| row.try_get::<i64>("", "total").ok())
                .unwrap_or(0) as u64
        };

        Ok(PaginatedResponse {
            data: responses,
            pagetotal: vec![crate::models::PageTotal { total }],
        })
    }

    // 获取考试题目 - 对应 gethtimu() 方法
    pub async fn get_exam_questions(&self, params: ExamQueryParams) -> Result<serde_json::Value> {
        let kjid = params.kjid;
        let biao = params.biao.unwrap_or_else(|| "fbsy".to_string());
        let type_ = params.type_.unwrap_or_else(|| "syzc".to_string());
        let fast_mode = params.fast.as_deref() == Some("1") || params.fast.as_deref() == Some("true");

        tracing::info!("📝 获取考试题目: kjid={}, biao={}, type={}, fast_mode={}", kjid, biao, type_, fast_mode);

        // 🔥 获取Redis中的cookie
        let mut redis_conn = self.redis.get_async_connection().await?;
        let cookie: String = AsyncCommands::get(&mut redis_conn, "fbcookie").await
            .unwrap_or_else(|_| String::new());

        // 构建HTTP客户端和请求头
        let client = reqwest::Client::new();
        let mut headers = reqwest::header::HeaderMap::new();
        if !cookie.is_empty() {
            headers.insert("Cookie", cookie.parse().unwrap_or_else(|_| reqwest::header::HeaderValue::from_static("")));
        }
        headers.insert("Content-Type", "application/x-www-form-urlencoded".parse().unwrap());
        headers.insert("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36 Edg/119.0.0.0".parse().unwrap());

        // 🚀 调用外部API获取Exercise数据
        tracing::info!("🌐 调用外部API: https://tiku.fenbi.com/combine/exercise/getExercise");
        let exercise_response = client
            .get("https://tiku.fenbi.com/combine/exercise/getExercise")
            .headers(headers.clone())
            .query(&[
                ("format", "html"),
                ("key", &kjid),
                ("routecs", "syzc"), // 固定使用syzc，与EggJS一致
                ("kav", "120"),
                ("av", "120"),
                ("hav", "120"),
                ("app", "web"),
            ])
            .send()
            .await?;

        let exercise_data: serde_json::Value = exercise_response.json().await?;
        tracing::info!("🔍 Exercise response code: {:?}", exercise_data.get("code"));
        tracing::info!("🔍 Exercise response msg: {:?}", exercise_data.get("msg"));

        // 🚀 快速模式：只返回基本字段（id和globalId）
        if fast_mode {
            tracing::info!("🚀 gethtimu快速模式：获取完整题目列表");

            // 🔥 关键修复：使用与非fastMode相同的逻辑来确定API端点和requestKey
            let (qs, rkey) = if exercise_data.get("code").and_then(|c| c.as_i64()) == Some(1) {
                let request_key = exercise_data
                    .get("data")
                    .and_then(|d| d.get("switchVO"))
                    .and_then(|s| s.get("requestKey"))
                    .and_then(|k| k.as_str())
                    .unwrap_or("");
                tracing::info!("🎯 使用exercise模式, requestKey: {}", request_key);
                ("exercise", request_key.to_string())
            } else {
                tracing::info!("🎯 需要获取solution数据...");

                // 获取Solution数据
                let solution_response = client
                    .get("https://tiku.fenbi.com/combine/exercise/getSolution")
                    .headers(headers.clone())
                    .query(&[
                        ("format", "html"),
                        ("key", &kjid),
                        ("routecs", "syzc"), // 固定使用syzc，与EggJS一致
                        ("kav", "120"),
                        ("av", "120"),
                        ("hav", "120"),
                        ("app", "web"),
                    ])
                    .send()
                    .await?;

                let solution_data: serde_json::Value = solution_response.json().await?;
                let request_key = solution_data
                    .get("data")
                    .and_then(|d| d.get("switchVO"))
                    .and_then(|s| s.get("requestKey"))
                    .and_then(|k| k.as_str())
                    .unwrap_or("");
                tracing::info!("🎯 使用solution模式, requestKey: {}", request_key);
                ("solution", request_key.to_string())
            };

            if rkey.is_empty() {
                tracing::warn!("🚨 未找到requestKey");
                return Ok(serde_json::Value::Array(vec![]));
            }

            // 检查消息状态
            let msg = exercise_data.get("msg").and_then(|m| m.as_str()).unwrap_or("");
            tracing::info!("📋 API消息状态: {}", msg);

            if msg != "SUCCESS" {
                tracing::warn!("🚨 API返回状态不是SUCCESS: {}", msg);
                return Ok(serde_json::Value::Array(vec![]));
            }

            // 调用static API获取题目列表
            tracing::info!("🌐 调用API: https://tiku.fenbi.com/combine/static/{}", qs);
            let static_response = client
                .get(&format!("https://tiku.fenbi.com/combine/static/{}", qs))
                .headers(headers)
                .query(&[
                    ("key", rkey.as_str()),
                    ("routecs", "syzc"), // 固定使用syzc，与EggJS一致
                    ("type", "1"),
                    ("kav", "120"),
                    ("av", "120"),
                    ("hav", "120"),
                    ("app", "web"),
                ])
                .send()
                .await?;

            let static_data: serde_json::Value = static_response.json().await?;

            // 🔥 关键修复：根据模式选择正确的数据字段
            let field_name = if qs == "exercise" { "questions" } else { "solutions" };
            let empty_vec = vec![];
            let questions = static_data.get(field_name).and_then(|q| q.as_array()).unwrap_or(&empty_vec);
            tracing::info!("🔍 获取到完整{}列表: {} 个题目", field_name, questions.len());

            if questions.is_empty() {
                tracing::warn!("⚠️ 获取到的题目列表为空");
                tracing::info!("🔍 完整响应数据: {}", serde_json::to_string_pretty(&static_data).unwrap_or_default());
            }

            // 🔥 修复：快速模式返回与EggJS完全一致的格式
            let fast_result: Vec<FastQuestionResponse> = questions
                .iter()
                .filter_map(|item| {
                    let id = item.get("id").and_then(|i| i.as_i64()).unwrap_or(0) as i32;
                    let global_id = item.get("globalId").and_then(|g| g.as_str()).unwrap_or("").to_string();

                    Some(FastQuestionResponse {
                        id,
                        global_id,
                    })
                })
                .collect();

            return Ok(serde_json::to_value(fast_result)?);
        }

        // 正常模式：获取完整题目信息
        let exam_paper_query = format!(
            "SELECT questionIds FROM fbkaojuan WHERE sid = '{}'",
            kjid
        );

        let exam_paper_result = self.db.query_one(
            Statement::from_string(
                self.db.get_database_backend(),
                exam_paper_query,
            )
        ).await?;

        if let Some(row) = exam_paper_result {
            if let Ok(question_ids_str) = row.try_get::<String>("", "questionIds") {
                let question_ids: Vec<i32> = question_ids_str
                    .split(',')
                    .filter_map(|s| s.parse::<i32>().ok())
                    .collect();

                if question_ids.is_empty() {
                    return Ok(serde_json::Value::Array(vec![]));
                }

                // 获取题目详情
                let questions_sql = format!(
                    "SELECT * FROM fbsy WHERE id IN ({})",
                    question_ids.iter().map(|id| id.to_string()).collect::<Vec<_>>().join(",")
                );

                let questions = Entity::find()
                    .from_raw_sql(Statement::from_string(
                        self.db.get_database_backend(),
                        questions_sql,
                    ))
                    .into_model::<Model>()
                    .all(&self.db)
                    .await?;

                // 按原始顺序排序
                let mut question_map: std::collections::HashMap<i32, Model> = questions
                    .into_iter()
                    .map(|q| (q.id, q))
                    .collect();

                let responses: Vec<QuestionResponse> = question_ids
                    .into_iter()
                    .filter_map(|id| {
                        question_map.remove(&id).map(|q| QuestionResponse {
                            id: q.id,
                            content: q.content,
                            ds: q.ds,
                            choice: q.choice,
                            answerone: q.answerone,
                            answertwo: q.answertwo,
                            answerthree: q.answerthree,
                            answerfour: q.answerfour,
                            answer: q.answer,
                            solution: q.solution,
                            source: q.source,
                            created_time: q.created_time,
                            allcateid: q.allcateid,
                            tag: q.tag,
                            correct_ratio: q.correct_ratio,
                            most_wrong_answer: q.most_wrong_answer,
                            material_keys: q.material_keys,
                            parentid: q.parentid,
                            material: q.material,
                            extra: q.extra,
                            canvas_data: q.canvas_data,
                            canvas_width: q.canvas_width,
                            canvas_height: q.canvas_height,
                            has_canvas: q.has_canvas,
                            canvas_updated_time: q.canvas_updated_time,
                        })
                    })
                    .collect();

                Ok(serde_json::to_value(responses)?)
            } else {
                Ok(serde_json::Value::Array(vec![]))
            }
        } else {
            Ok(serde_json::Value::Array(vec![]))
        }
    }

    // 获取题目排名 - 对应 gettimurank() 方法
    pub async fn get_question_rank(&self, params: RankQueryParams) -> Result<RankListResponse> {
        let biao = params.biao;
        let id = params.id;
        let allcateid = params.allcateid;
        let fast_mode = params.fast.as_deref() == Some("1") || params.fast.as_deref() == Some("true");

        let cateids: Vec<i32> = allcateid
            .split(',')
            .filter_map(|s| s.parse::<i32>().ok())
            .collect();

        tracing::info!(
            "🔍 gettimurank: biao={}, id={}, cateids={}个, fastMode={}",
            biao, id, cateids.len(), fast_mode
        );

        if cateids.is_empty() {
            return Ok(RankListResponse { res: vec![] });
        }

        // 🚀 优化1：批量获取分类信息，减少数据库连接
        tracing::info!("🔍 批量获取分类信息");
        let cate_query = format!(
            "SELECT id, name FROM fbsycate WHERE id IN ({})",
            cateids.iter().map(|id| id.to_string()).collect::<Vec<_>>().join(",")
        );

        let cate_results = self.db.query_all(
            Statement::from_string(
                self.db.get_database_backend(),
                cate_query,
            )
        ).await?;

        let mut cate_map = std::collections::HashMap::new();
        for row in cate_results {
            if let (Ok(cate_id), Ok(cate_name)) = (
                row.try_get::<i32>("", "id"),
                row.try_get::<String>("", "name")
            ) {
                cate_map.insert(cate_id, cate_name);
            }
        }

        // 🚀 优化2：分批处理排名查询，避免数据库压力
        let mut results = Vec::new();
        let batch_size = if fast_mode { 5 } else { 3 };

        tracing::info!("🔍 开始分批查询排名，批次大小: {}", batch_size);

        for i in (0..cateids.len()).step_by(batch_size) {
            let batch = &cateids[i..std::cmp::min(i + batch_size, cateids.len())];
            tracing::info!("🔍 处理批次 {}: {:?}", i / batch_size + 1, batch);

            // 并行处理当前批次
            let batch_futures: Vec<_> = batch
                .iter()
                .map(|&cateid| {
                    let rank_sql = format!(
                        "SELECT COUNT(*) + 1 AS rank FROM {} WHERE allcateid LIKE '%{}%' AND id > {}",
                        biao, cateid, id
                    );

                    let cate_map_clone = cate_map.clone();
                    let db_clone = self.db.clone();

                    async move {
                        match db_clone.query_one(
                            Statement::from_string(
                                db_clone.get_database_backend(),
                                rank_sql,
                            )
                        ).await {
                            Ok(Some(row)) => {
                                if let Ok(rank) = row.try_get::<i64>("", "rank") {
                                    RankResponse {
                                        rank: rank as u32,
                                        name: cate_map_clone.get(&cateid).cloned().unwrap_or_else(|| format!("分类{}", cateid)),
                                        cateid,
                                    }
                                } else {
                                    RankResponse {
                                        rank: 999,
                                        name: cate_map_clone.get(&cateid).cloned().unwrap_or_else(|| format!("分类{}", cateid)),
                                        cateid,
                                    }
                                }
                            },
                            _ => RankResponse {
                                rank: 999,
                                name: cate_map_clone.get(&cateid).cloned().unwrap_or_else(|| format!("分类{}", cateid)),
                                cateid,
                            },
                        }
                    }
                })
                .collect();

            let batch_results = futures::future::join_all(batch_futures).await;
            results.extend(batch_results);

            // 批次间延迟，避免数据库压力
            if i + batch_size < cateids.len() {
                let delay = if fast_mode { 5 } else { 10 };
                tokio::time::sleep(tokio::time::Duration::from_millis(delay)).await;
            }
        }

        tracing::info!("🔍 排名查询完成: {} 个分类", results.len());
        Ok(RankListResponse { res: results })
    }

    // 保存页面状态 - 对应 remeberpage() 方法
    pub async fn save_page_state(&self, params: PageStateSaveParams) -> Result<serde_json::Value> {
        let cateid = params.cateid;
        let kjid = params.kjid.clone();
        let catesid = params.catesid.clone();
        let page = params.page;
        let type_ = params.type_;

        tracing::info!(
            "💾 保存页面状态: cateid={:?}, kjid={:?}, catesid={:?}, page={}, type={}",
            cateid, kjid, catesid, page, type_
        );

        // 构建查询条件
        let where_condition = if let Some(kjid_val) = &kjid {
            format!("kjid = '{}' AND type = '{}'", kjid_val, type_)
        } else if let Some(cateid_val) = cateid {
            format!("cateid = {} AND type = '{}'", cateid_val, type_)
        } else {
            return Ok(serde_json::json!({
                "success": false,
                "message": "缺少必要参数：kjid或cateid"
            }));
        };

        // 检查是否已存在记录
        let check_sql = format!(
            "SELECT id FROM fbremeber WHERE {}",
            where_condition
        );

        let existing_record = self.db.query_one(
            Statement::from_string(self.db.get_database_backend(), check_sql)
        ).await?;

        if let Some(_) = existing_record {
            // 更新现有记录
            let update_sql = format!(
                "UPDATE fbremeber SET page = {} WHERE {}",
                page, where_condition
            );

            let result = self.db.execute(Statement::from_string(
                self.db.get_database_backend(),
                update_sql,
            )).await?;

            tracing::info!("✅ 更新页面状态成功: page={}, affected_rows={}", page, result.rows_affected());

            // 返回与EggJS一致的MySQL结果格式
            Ok(serde_json::json!({
                "fieldCount": 0,
                "affectedRows": result.rows_affected(),
                "insertId": 0,
                "serverStatus": 34,
                "warningCount": 0,
                "message": "Rows matched: 1  Changed: 0  Warnings: 0",
                "protocol41": true,
                "changedRows": 0
            }))
        } else {
            // 插入新记录
            let insert_sql = format!(
                "INSERT INTO fbremeber (cateid, kjid, catesid, page, type) VALUES ({}, '{}', '{}', {}, '{}')",
                cateid.map(|c| c.to_string()).unwrap_or_else(|| "NULL".to_string()),
                kjid.as_ref().unwrap_or(&"NULL".to_string()),
                catesid.as_ref().unwrap_or(&"NULL".to_string()),
                page,
                type_
            );

            let result = self.db.execute(Statement::from_string(
                self.db.get_database_backend(),
                insert_sql,
            )).await?;

            tracing::info!("✅ 插入页面状态成功: page={}, affected_rows={}", page, result.rows_affected());

            // 返回与EggJS一致的MySQL结果格式
            Ok(serde_json::json!({
                "fieldCount": 0,
                "affectedRows": result.rows_affected(),
                "insertId": result.last_insert_id(),
                "serverStatus": 34,
                "warningCount": 0,
                "message": "Rows matched: 1  Changed: 0  Warnings: 0",
                "protocol41": true,
                "changedRows": 0
            }))
        }
    }

    // 恢复页面状态 - 对应 recoverpage() 方法
    pub async fn recover_page_state(&self, params: PageStateQueryParams) -> Result<SimplePageStateResponse> {
        let cateid = params.cateid;
        let kjid = params.kjid;
        let catesid = params.catesid;
        let type_ = params.type_.unwrap_or_else(|| "syzc".to_string());

        tracing::info!(
            "🔍 恢复页面状态: cateid={:?}, kjid={:?}, catesid={:?}, type={}",
            cateid, kjid, catesid, type_
        );

        // 构建查询条件
        let where_condition = if let Some(kjid_val) = &kjid {
            format!("kjid = '{}' AND type = '{}'", kjid_val, type_)
        } else if let Some(cateid_val) = cateid {
            format!("cateid = {} AND type = '{}'", cateid_val, type_)
        } else {
            // 如果没有必要参数，返回默认值1，与EggJS保持一致
            return Ok(SimplePageStateResponse { page: 1 });
        };

        // 查询页面状态
        let query_sql = format!(
            "SELECT page, cateid, kjid, catesid, type FROM fbremeber WHERE {}",
            where_condition
        );

        let result = self.db.query_one(
            Statement::from_string(self.db.get_database_backend(), query_sql)
        ).await?;

        if let Some(row) = result {
            let page: i32 = row.try_get("", "page").unwrap_or(1);
            tracing::info!("✅ 恢复页面状态成功: page={}", page);
            Ok(SimplePageStateResponse { page })
        } else {
            tracing::info!("⚠️ 未找到页面状态记录，返回默认值");
            // 与EggJS保持一致，未找到记录时返回page: 1
            Ok(SimplePageStateResponse { page: 1 })
        }
    }

    // 恢复页面状态（Egg.js兼容）- 对应 /egg/fbrecoverpage 接口
    pub async fn egg_recover_page_state(&self, params: PageStateQueryParams) -> Result<SimplePageStateResponse> {
        // 调用相同的恢复逻辑
        self.recover_page_state(params).await
    }

    // 获取题目评论 - 对应 pinglun() 方法
    pub async fn get_question_comments(&self, params: CommentQueryParams) -> Result<serde_json::Value> {
        let id = params.id;
        let type_ = params.type_.unwrap_or_else(|| "xingce".to_string());

        tracing::info!(
            "💬 获取题目评论: id={}, type={}",
            id, type_
        );

        // 🔥 获取Redis中的cookie - 完全按照EggJS逻辑
        let mut redis_conn = self.redis.get_async_connection().await?;
        let cookie: String = AsyncCommands::get(&mut redis_conn, "fbcookie").await
            .unwrap_or_else(|_| String::new());

        tracing::info!("🍪 获取到的Cookie长度: {}", cookie.len());

        // 构建请求头 - 完全按照EggJS逻辑
        let mut headers = reqwest::header::HeaderMap::new();
        headers.insert("Cookie", cookie.parse().unwrap_or_else(|_| reqwest::header::HeaderValue::from_static("")));
        headers.insert("Content-Type", "application/x-www-form-urlencoded".parse().unwrap());
        headers.insert("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36 Edg/119.0.0.0".parse().unwrap());

        // 请求问题集信息 - 完全按照EggJS逻辑
        let infourl = "https://ke.fenbi.com/api/gwy/v3/episodes/question_episodes_with_multi_type";
        tracing::info!("🔍 第一步API调用: {}", infourl);
        tracing::info!("📝 请求参数: question_ids={}, tiku_prefix={}", id, type_);

        let client = reqwest::Client::new();

        // 第一步API调用 - 完全按照EggJS逻辑
        match client.get(infourl)
            .headers(headers.clone())
            .query(&[
                ("question_ids", &id),
                ("tiku_prefix", &type_),
            ])
            .send()
            .await {
                Ok(info_response) => {
                    tracing::info!("✅ 第一步API调用成功，状态码: {}", info_response.status());

                    let info_data: serde_json::Value = info_response.json().await?;
                    tracing::info!("📊 第一步API响应数据结构: {:?}", info_data.as_object().map(|obj| obj.keys().collect::<Vec<_>>()));

                    // 提取plid - 完全按照EggJS逻辑: infoResponse.data?.data?.[id]?.[0]?.id
                    let plid = info_data
                        .get("data")
                        .and_then(|d| d.get(&id))
                        .and_then(|obj| obj.get("0"))
                        .and_then(|obj| obj.get("id"))
                        .and_then(|id_val| id_val.as_i64().map(|i| i.to_string()));

                    if let Some(plid_str) = plid {
                        tracing::info!("✅ 找到题目ID: {}", plid_str);

                        // 请求评论信息 - 完全按照EggJS逻辑
                        let comment_url = format!("https://ke.fenbi.com/api/gwy/v3/comments/episodes/{}", plid_str);
                        tracing::info!("🔍 第二步API调用: {}", comment_url);

                        // 第二步API调用 - 完全按照EggJS逻辑
                        match client.get(&comment_url)
                            .headers(headers)
                            .query(&[
                                ("len", "300"),
                                ("start", "0"),
                            ])
                            .send()
                            .await {
                                Ok(comment_response) => {
                                    tracing::info!("✅ 第二步API调用成功，状态码: {}", comment_response.status());
                                    let comment_data: serde_json::Value = comment_response.json().await?;

                                    tracing::info!("✅ 获取评论成功，响应数据结构: {:?}", comment_data.as_object().map(|obj| obj.keys().collect::<Vec<_>>()));

                                    // 直接返回原始API响应，与EggJS保持一致: ctx.body = commentResponse.data
                                    Ok(comment_data)
                                },
                                Err(e) => {
                                    tracing::error!("❌ 获取评论失败: {}", e);
                                    // 返回与EggJS一致的错误格式
                                    Ok(serde_json::json!({
                                        "success": false,
                                        "message": "请求失败",
                                        "code": 200,
                                        "error": e.to_string()
                                    }))
                                }
                            }
                    } else {
                        tracing::warn!("⚠️ 未找到对应题目或评论");
                        tracing::info!("🔍 调试信息 - info_data.data: {:?}", info_data.get("data"));
                        tracing::info!("🔍 调试信息 - info_data.data[{}]: {:?}", id, info_data.get("data").and_then(|d| d.get(&id)));

                        // 返回与EggJS一致的错误格式: ctx.body = { success: false, message: '未找到对应题目或评论', code: 200 }
                        Ok(serde_json::json!({
                            "success": false,
                            "message": "未找到对应题目或评论",
                            "code": 200
                        }))
                    }
                },
                Err(e) => {
                    tracing::error!("❌ 请求问题集信息失败: {}", e);
                    // 返回与EggJS一致的错误格式: ctx.body = { success: false, message: '请求失败', code: 200, error: error.message }
                    Ok(serde_json::json!({
                        "success": false,
                        "message": "请求失败",
                        "code": 200,
                        "error": e.to_string()
                    }))
                }
            }
    }
}
