name: Build Linux Musl

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Install Rust toolchain
        uses: dtolnay/rust-toolchain@stable
        with:
          target: x86_64-unknown-linux-musl

      - name: Install musl-tools
        run: sudo apt-get update && sudo apt-get install -y musl-tools

      - name: Build for linux64-musl
        env:
          PKG_CONFIG_ALLOW_CROSS: 1
        run: cargo build --release --target x86_64-unknown-linux-musl

      - name: Upload artifact
        uses: actions/upload-artifact@v4
        with:
          name: egg-rust-api-linux-musl
          path: target/x86_64-unknown-linux-musl/release/egg-rust-api
