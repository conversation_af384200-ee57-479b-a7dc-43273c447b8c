use std::env;
use anyhow::Result;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct Config {
    pub database_url: String,
    pub redis_url: String,
    pub server_host: String,
    pub server_port: u16,
}

//默认"mysql://root:wangcong@************:11436/zz"，"redis://************:2639/0"
//启动加-s 服务器模式，默认是本地模式，"mysql://root:wangcong127.0.0.1:3306/zz"
impl Config {
    pub fn from_env() -> Result<Self> {
        // 检查命令行参数，判断是否为服务器模式
        let args: Vec<String> = env::args().collect();
        let is_server_mode = args.iter().any(|arg| arg == "-s");

        // 检查端口参数
        let mut server_port = 8001; // 默认端口改为8001
        if let Some(port_index) = args.iter().position(|arg| arg == "-p") {
            if port_index + 1 < args.len() {
                if let Ok(port) = args[port_index + 1].parse::<u16>() {
                    server_port = port;
                    tracing::info!("🎯 使用自定义端口: {}", server_port);
                } else {
                    tracing::warn!("⚠️ 无效的端口号，使用默认端口: {}", server_port);
                }
            }
        }

        let (database_url, redis_url) = if is_server_mode {
            // 服务器模式：使用本地数据库
            tracing::info!("🚀 服务器模式：使用本地数据库配置");
            (
                "mysql://root:wangcong@127.0.0.1:3306/zz".to_string(),
                env::var("REDIS_URL")
                    .unwrap_or_else(|_| "redis://127.0.0.1:6379/0".to_string())
            )
        } else {
            // 默认模式：使用远程数据库
            tracing::info!("💻 本地模式：使用远程数据库配置");
            (
                env::var("DATABASE_URL")
                    .unwrap_or_else(|_| "mysql://root:wangcong@************:11436/zz".to_string()),
                env::var("REDIS_URL")
                    .unwrap_or_else(|_| "redis://************:2639/0".to_string())
            )
        };

        tracing::info!("加载的 DATABASE_URL: {}", database_url);
        tracing::info!("加载的 REDIS_URL: {}", redis_url);

        Ok(Config {
            database_url,
            redis_url,
            server_host: env::var("SERVER_HOST")
                .unwrap_or_else(|_| "127.0.0.1".to_string()),
            server_port: env::var("SERVER_PORT")
                .unwrap_or_else(|_| server_port.to_string())
                .parse()
                .unwrap_or(server_port),
        })
    }
}
