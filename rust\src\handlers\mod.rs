use actix_web::{web, HttpResponse, HttpRequest};
use crate::models::*;
use crate::services::QuestionService;

// 健康检查
pub async fn health_check() -> HttpResponse {
    HttpResponse::Ok().json(serde_json::json!({
        "status": "ok",
        "message": "Rust API 服务运行正常"
    }))
}

// 获取题目数据 - 对应 timu() 方法
pub async fn get_questions(
    req: HttpRequest,
    service: web::Data<QuestionService>,
) -> HttpResponse {
    // 从查询参数构建 QuestionQueryParams
    let query_string = req.query_string();
    let params = parse_query_params(&query_string);

    tracing::info!("📝 获取题目请求: {:?}", params);

    match service.get_questions(params).await {
        Ok(response) => {
            tracing::info!("✅ 获取题目成功，返回 {} 条数据", response.data.len());
            HttpResponse::Ok().json(response)
        }
        Err(e) => {
            tracing::error!("❌ 获取题目失败: {}", e);
            HttpResponse::InternalServerError().json(serde_json::json!({
                "error": "获取题目失败",
                "message": e.to_string()
            }))
        }
    }
}

// 获取考试题目 - 对应 gethtimu() 方法
pub async fn get_exam_questions(
    req: HttpRequest,
    service: web::Data<QuestionService>,
) -> HttpResponse {
    // 从查询参数构建 ExamQueryParams
    let query_string = req.query_string();
    let params = parse_exam_query_params(&query_string);

    tracing::info!("📝 获取考试题目请求: {:?}", params);

    match service.get_exam_questions(params).await {
        Ok(response) => {
            let count = if response.is_array() { response.as_array().unwrap().len() } else { 0 };
            tracing::info!("✅ 获取考试题目成功，返回 {} 条数据", count);
            HttpResponse::Ok().json(response)
        }
        Err(e) => {
            tracing::error!("❌ 获取考试题目失败: {}", e);
            HttpResponse::InternalServerError().json(serde_json::json!({
                "error": "获取考试题目失败",
                "message": e.to_string()
            }))
        }
    }
}

// 获取题目排名 - 对应 gettimurank() 方法
pub async fn get_question_rank(
    req: HttpRequest,
    service: web::Data<QuestionService>,
) -> HttpResponse {
    // 从查询参数构建 RankQueryParams
    let query_string = req.query_string();
    let params = parse_rank_query_params(&query_string);

    tracing::info!("📝 获取题目排名请求: {:?}", params);

    match service.get_question_rank(params).await {
        Ok(response) => {
            tracing::info!("✅ 获取题目排名成功，返回 {} 个分类", response.res.len());
            HttpResponse::Ok().json(response)
        }
        Err(e) => {
            tracing::error!("❌ 获取题目排名失败: {}", e);
            HttpResponse::InternalServerError().json(serde_json::json!({
                "error": "获取题目排名失败",
                "message": e.to_string()
            }))
        }
    }
}

// 保存页面状态 - 对应 remeberpage() 方法
pub async fn save_page_state(
    req: HttpRequest,
    service: web::Data<QuestionService>,
    body: web::Json<PageStateSaveParams>,
) -> HttpResponse {
    let params = body.into_inner();

    tracing::info!("📝 保存页面状态请求: {:?}", params);

    match service.save_page_state(params).await {
        Ok(response) => {
            tracing::info!("✅ 保存页面状态成功");
            HttpResponse::Ok().json(response)
        }
        Err(e) => {
            tracing::error!("❌ 保存页面状态失败: {}", e);
            HttpResponse::InternalServerError().json(serde_json::json!({
                "error": "保存页面状态失败",
                "message": e.to_string()
            }))
        }
    }
}

// 恢复页面状态 - 对应 recoverpage() 方法
pub async fn recover_page_state(
    req: HttpRequest,
    service: web::Data<QuestionService>,
) -> HttpResponse {
    // 从查询参数构建 PageStateQueryParams
    let query_string = req.query_string();
    let params = parse_page_state_query_params(&query_string);

    tracing::info!("📝 恢复页面状态请求: {:?}", params);

    match service.egg_recover_page_state(params).await {
        Ok(response) => {
            tracing::info!("✅ 恢复页面状态成功");
            HttpResponse::Ok().json(response)
        }
        Err(e) => {
            tracing::error!("❌ 恢复页面状态失败: {}", e);
            HttpResponse::InternalServerError().json(serde_json::json!({
                "error": "恢复页面状态失败",
                "message": e.to_string()
            }))
        }
    }
}

// 获取题目评论 - 对应 pinglun() 方法
pub async fn get_question_comments(
    req: HttpRequest,
    service: web::Data<QuestionService>,
) -> HttpResponse {
    // 从查询参数构建 CommentQueryParams
    let query_string = req.query_string();
    let params = parse_comment_query_params(&query_string);

    tracing::info!("📝 获取题目评论请求: {:?}", params);

    match service.get_question_comments(params).await {
        Ok(response) => {
            tracing::info!("✅ 获取题目评论成功");
            HttpResponse::Ok().json(response)
        }
        Err(e) => {
            tracing::error!("❌ 获取题目评论失败: {}", e);
            // 返回与EggJS一致的错误格式
            HttpResponse::Ok().json(serde_json::json!({
                "success": false,
                "message": "请求失败",
                "code": 200,
                "error": e.to_string()
            }))
        }
    }
}

// 解析查询参数
fn parse_query_params(query_string: &str) -> QuestionQueryParams {
    let mut params = QuestionQueryParams {
        id: None,
        per: None,
        page: None,
        type_: None,
        z: None,
        b: None,
        t: None,
        gen: None,
        ids: None,
        kjid: None,
        f: None,
        o: None,
        q: None,
        biao: None,
        parentid: None,
        fast: None,
        zql: None,
    };

    for pair in query_string.split('&') {
        if let Some((key, value)) = pair.split_once('=') {
            let decoded_value = urlencoding::decode(value).unwrap_or_else(|_| value.into());
            match key {
                "id" => params.id = decoded_value.parse().ok(),
                "per" => params.per = decoded_value.parse().ok(),
                "page" => params.page = decoded_value.parse().ok(),
                "type" => params.type_ = Some(decoded_value.to_string()),
                "z" => params.z = decoded_value.parse().ok(),
                "b" => params.b = decoded_value.parse().ok(),
                "t" => params.t = decoded_value.parse().ok(),
                "gen" => params.gen = decoded_value.parse().ok(),
                "ids" => params.ids = Some(decoded_value.to_string()),
                "kjid" => params.kjid = Some(decoded_value.to_string()),
                "f" => params.f = decoded_value.parse().ok(),
                "o" => params.o = decoded_value.parse().ok(),
                "q" => params.q = Some(decoded_value.to_string()),
                "biao" => params.biao = Some(decoded_value.to_string()),
                "parentid" => params.parentid = decoded_value.parse().ok(),
                "fast" => params.fast = Some(decoded_value.to_string()),
                "zql" => params.zql = decoded_value.parse().ok(),
                _ => {}
            }
        }
    }

    params
}

// 解析考试查询参数
fn parse_exam_query_params(query_string: &str) -> ExamQueryParams {
    let mut params = ExamQueryParams {
        kjid: String::new(),
        biao: None,
        type_: None,
        fast: None,
        per: None,
        page: None,
        id: None,
        z: None,
        b: None,
        f: None,
        o: None,
        ids: None,
        t: None,
        zql: None,
    };

    for pair in query_string.split('&') {
        if let Some((key, value)) = pair.split_once('=') {
            let decoded_value = urlencoding::decode(value).unwrap_or_else(|_| value.into());
            match key {
                "kjid" => params.kjid = decoded_value.to_string(),
                "biao" => params.biao = Some(decoded_value.to_string()),
                "type" => params.type_ = Some(decoded_value.to_string()),
                "fast" => params.fast = Some(decoded_value.to_string()),
                "per" => params.per = decoded_value.parse().ok(),
                "page" => params.page = decoded_value.parse().ok(),
                "id" => params.id = decoded_value.parse().ok(),
                "z" => params.z = decoded_value.parse().ok(),
                "b" => params.b = decoded_value.parse().ok(),
                "f" => params.f = decoded_value.parse().ok(),
                "o" => params.o = decoded_value.parse().ok(),
                "ids" => params.ids = Some(decoded_value.to_string()),
                "t" => params.t = decoded_value.parse().ok(),
                "zql" => params.zql = decoded_value.parse().ok(),
                _ => {}
            }
        }
    }

    params
}

// 解析排名查询参数
fn parse_rank_query_params(query_string: &str) -> RankQueryParams {
    let mut params = RankQueryParams {
        biao: String::new(),
        id: 0,
        allcateid: String::new(),
        fast: None,
    };

    for pair in query_string.split('&') {
        if let Some((key, value)) = pair.split_once('=') {
            match key {
                "biao" => params.biao = value.to_string(),
                "id" => params.id = value.parse().unwrap_or(0),
                "allcateid" => params.allcateid = value.to_string(),
                "fast" => params.fast = Some(value.to_string()),
                _ => {}
            }
        }
    }

    params
}

// 解析页面状态保存参数
fn parse_page_state_save_params(query_string: &str) -> PageStateSaveParams {
    let mut params = PageStateSaveParams {
        cateid: None,
        kjid: None,
        catesid: None,
        page: 1,
        type_: "xingce".to_string(),
    };

    for pair in query_string.split('&') {
        if let Some((key, value)) = pair.split_once('=') {
            let decoded_value = urlencoding::decode(value).unwrap_or_else(|_| value.into());
            match key {
                "cateid" => params.cateid = decoded_value.parse().ok(),
                "kjid" => params.kjid = Some(decoded_value.to_string()),
                "catesid" => params.catesid = Some(decoded_value.to_string()),
                "page" => params.page = decoded_value.parse().unwrap_or(1),
                "type" => params.type_ = decoded_value.to_string(),
                _ => {}
            }
        }
    }

    params
}

// 解析页面状态查询参数
fn parse_page_state_query_params(query_string: &str) -> PageStateQueryParams {
    let mut params = PageStateQueryParams {
        cateid: None,
        kjid: None,
        catesid: None,
        type_: None,
    };

    for pair in query_string.split('&') {
        if let Some((key, value)) = pair.split_once('=') {
            let decoded_value = urlencoding::decode(value).unwrap_or_else(|_| value.into());
            match key {
                "cateid" => params.cateid = decoded_value.parse().ok(),
                "kjid" => params.kjid = Some(decoded_value.to_string()),
                "catesid" => params.catesid = Some(decoded_value.to_string()),
                "type" => params.type_ = Some(decoded_value.to_string()),
                _ => {}
            }
        }
    }

    params
}

// 解析评论查询参数
fn parse_comment_query_params(query_string: &str) -> CommentQueryParams {
    let mut params = CommentQueryParams {
        id: "5911268".to_string(),
        type_: Some("xingce".to_string()),
    };

    for pair in query_string.split('&') {
        if let Some((key, value)) = pair.split_once('=') {
            let decoded_value = urlencoding::decode(value).unwrap_or_else(|_| value.into());
            match key {
                "id" => params.id = decoded_value.to_string(),
                "type" => params.type_ = Some(decoded_value.to_string()),
                _ => {}
            }
        }
    }

    params
}
