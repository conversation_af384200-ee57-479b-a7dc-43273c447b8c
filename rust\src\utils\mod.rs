use sea_orm::*;
use redis::Client as RedisClient;
use anyhow::Result;
use crate::config::Config;

// 初始化数据库连接
pub async fn init_database(config: &Config) -> Result<DatabaseConnection> {
    let db = Database::connect(&config.database_url).await?;
    tracing::info!("数据库连接成功");
    Ok(db)
}

// 初始化 Redis 连接
pub fn init_redis(config: &Config) -> Result<RedisClient> {
    let client = RedisClient::open(config.redis_url.as_str())?;
    tracing::info!("Redis 连接成功");
    Ok(client)
}

// 测试数据库连接
pub async fn test_database_connection(db: &DatabaseConnection) -> Result<()> {
    let result = db.ping().await?;
    tracing::info!("数据库连接测试成功: {:?}", result);
    Ok(())
}

// 测试 Redis 连接
pub async fn test_redis_connection(client: &RedisClient) -> Result<()> {
    let mut conn = client.get_async_connection().await?;
    let result: String = redis::cmd("PING").query_async(&mut conn).await?;
    tracing::info!("Redis 连接测试成功: {}", result);
    Ok(())
}
