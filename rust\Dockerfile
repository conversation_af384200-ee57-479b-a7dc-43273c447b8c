# Stage 1: Builder
FROM rustlang/rust:nightly AS builder

# Install musl-tools for static compilation
RUN apt-get update && apt-get install -y musl-tools pkg-config && rm -rf /var/lib/apt/lists/*

# Add musl target
RUN rustup target add x86_64-unknown-linux-musl

WORKDIR /app

# Copy Cargo.toml and Cargo.lock first to leverage Docker cache
COPY Cargo.toml Cargo.lock ./

# This step is to cache dependencies. It will fail if src is not present,
# but it will ensure that if only src changes, dependencies are not re-downloaded.
# We'll create a dummy src/main.rs to make cargo build happy.
RUN mkdir src && echo "fn main() {}" > src/main.rs
RUN PKG_CONFIG_ALLOW_CROSS=1 cargo build --release --target x86_64-unknown-linux-musl --locked || true
RUN rm -rf src

# Copy the rest of the source code
COPY . .

# Build the release binary for musl target
ENV PKG_CONFIG_ALLOW_CROSS=1
RUN cargo build --release --target x86_64-unknown-linux-musl

# Stage 2: Runner
FROM alpine:latest

# Install ca-certificates for HTTPS if your application makes outgoing HTTPS requests
RUN apk add --no-cache ca-certificates

WORKDIR /app

# Copy the compiled binary from the builder stage
COPY --from=builder /app/target/x86_64-unknown-linux-musl/release/egg-rust-api ./egg-rust-api

# Expose the port your application listens on (e.g., 8000, adjust if different)
EXPOSE 8000

# Run the application
CMD ["./egg-rust-api"]
