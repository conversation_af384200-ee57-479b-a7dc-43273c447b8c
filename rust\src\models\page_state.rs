use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

// 页面状态模型 - 对应 fbremeber 表
#[derive(Debug, <PERSON>lone, PartialEq, DeriveEntityModel, Serialize, Deserialize)]
#[sea_orm(table_name = "fbremeber")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i32,
    pub cateid: Option<i32>,
    pub kjid: Option<String>,
    pub catesid: Option<String>,
    pub page: i32,
    #[serde(rename = "type")]
    pub type_: String,
    pub created_time: Option<DateTime<Utc>>,
    pub updated_time: Option<DateTime<Utc>>,
}

#[derive(Co<PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
