use serde::{Deserialize, Serialize};
use sea_orm::entity::prelude::*;
use chrono::{DateTime, Utc};
use sea_orm::ActiveModelBehavior;

pub mod page_state;
pub use page_state::*;

// 题目模型 - 对应 fbsy 表
#[derive(Debug, Clone, PartialEq, DeriveEntityModel, Serialize, Deserialize)]
#[sea_orm(table_name = "fbsy")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i32,
    pub content: Option<String>,
    pub ds: Option<String>,
    pub choice: Option<String>,
    pub answerone: Option<String>,
    pub answertwo: Option<String>,
    pub answerthree: Option<String>,
    pub answerfour: Option<String>,
    pub answer: Option<String>,
    pub solution: Option<String>,
    pub source: Option<String>,
    pub created_time: Option<String>,
    pub allcateid: Option<String>,
    pub tag: Option<i32>,
    #[sea_orm(column_name = "correctRatio")]
    pub correct_ratio: Option<f64>,
    pub most_wrong_answer: Option<String>,
    pub material_keys: Option<String>,
    pub parentid: Option<i32>,
    pub material: Option<String>,
    pub extra: Option<String>,
    pub canvas_data: Option<String>,
    pub canvas_width: Option<i32>,
    pub canvas_height: Option<i32>,
    pub has_canvas: Option<bool>,
    pub canvas_updated_time: Option<DateTime<Utc>>,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}

// API 请求/响应结构
#[derive(Debug, Serialize, Deserialize)]
pub struct QuestionQueryParams {
    pub id: Option<i32>,
    pub per: Option<u32>,
    pub page: Option<u32>,
    #[serde(rename = "type")]
    pub type_: Option<String>,
    pub z: Option<i32>,
    pub b: Option<i32>,
    pub t: Option<i32>,
    pub gen: Option<i32>,
    pub ids: Option<String>,
    pub kjid: Option<String>,
    pub f: Option<i32>,
    pub o: Option<i32>,
    pub q: Option<String>,
    pub biao: Option<String>,
    pub parentid: Option<i32>,
    pub fast: Option<String>,
    pub zql: Option<i32>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct QuestionResponse {
    pub id: i32,
    pub content: Option<String>,
    pub ds: Option<String>,
    pub choice: Option<String>,
    pub answerone: Option<String>,
    pub answertwo: Option<String>,
    pub answerthree: Option<String>,
    pub answerfour: Option<String>,
    pub answer: Option<String>,
    pub solution: Option<String>,
    pub source: Option<String>,
    pub created_time: Option<String>,
    pub allcateid: Option<String>,
    pub tag: Option<i32>,
    pub correct_ratio: Option<f64>,
    pub most_wrong_answer: Option<String>,
    pub material_keys: Option<String>,
    pub parentid: Option<i32>,
    pub material: Option<String>,
    pub extra: Option<String>,
    pub canvas_data: Option<String>,
    pub canvas_width: Option<i32>,
    pub canvas_height: Option<i32>,
    pub has_canvas: Option<bool>,
    pub canvas_updated_time: Option<DateTime<Utc>>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PaginatedResponse<T> {
    pub data: Vec<T>,
    pub pagetotal: Vec<PageTotal>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PageTotal {
    pub total: u64,
}

// 考试题目查询参数
#[derive(Debug, Serialize, Deserialize)]
pub struct ExamQueryParams {
    pub kjid: String,
    pub biao: Option<String>,
    #[serde(rename = "type")]
    pub type_: Option<String>,
    pub fast: Option<String>,
    pub per: Option<u32>,
    pub page: Option<u32>,
    pub id: Option<i32>,
    pub z: Option<i32>,
    pub b: Option<i32>,
    pub f: Option<i32>,
    pub o: Option<i32>,
    pub ids: Option<String>,
    pub t: Option<i32>,
    pub zql: Option<i32>,
}

// 排名查询参数
#[derive(Debug, Serialize, Deserialize)]
pub struct RankQueryParams {
    pub biao: String,
    pub id: i32,
    pub allcateid: String,
    pub fast: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct RankResponse {
    pub rank: u32,
    pub name: String,
    pub cateid: i32,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct RankListResponse {
    pub res: Vec<RankResponse>,
}

// 快速模式的简化响应结构
#[derive(Debug, Serialize, Deserialize)]
pub struct FastQuestionResponse {
    pub id: i32,
    #[serde(rename = "globalId")]
    pub global_id: String,
}

// 页面状态保存参数
#[derive(Debug, Serialize, Deserialize)]
pub struct PageStateSaveParams {
    pub cateid: Option<i32>,
    pub kjid: Option<String>,
    pub catesid: Option<String>,
    pub page: i32,
    #[serde(rename = "type")]
    pub type_: String,
}

// 页面状态查询参数
#[derive(Debug, Serialize, Deserialize)]
pub struct PageStateQueryParams {
    pub cateid: Option<i32>,
    pub kjid: Option<String>,
    pub catesid: Option<String>,
    #[serde(rename = "type")]
    pub type_: Option<String>,
}

// 页面状态响应
#[derive(Debug, Serialize, Deserialize)]
pub struct PageStateResponse {
    pub success: bool,
    pub message: String,
    pub data: Option<PageStateData>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PageStateData {
    pub page: i32,
    pub cateid: Option<i32>,
    pub kjid: Option<String>,
    pub catesid: Option<String>,
    #[serde(rename = "type")]
    pub type_: String,
}

// 简单的页面状态响应（与EggJS保持一致）
#[derive(Debug, Serialize, Deserialize)]
pub struct SimplePageStateResponse {
    pub page: i32,
}

// 评论查询参数
#[derive(Debug, Serialize, Deserialize)]
pub struct CommentQueryParams {
    pub id: String,
    #[serde(rename = "type")]
    pub type_: Option<String>,
}

// 评论响应
#[derive(Debug, Serialize, Deserialize)]
pub struct CommentResponse {
    pub success: bool,
    pub message: String,
    pub data: Option<Vec<CommentData>>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CommentData {
    pub id: String,
    pub content: String,
    pub user: String,
    pub time: String,
    pub likes: i32,
}
