[package]
name = "egg-rust-api"
version = "0.1.0"
edition = "2021"

[dependencies]
# Web 框架
actix-web = "4.4"
actix-cors = "0.6"
actix-files = "0.6"

# 数据库 ORM
sea-orm = { version = "0.12", features = ["sqlx-mysql", "runtime-async-std-rustls", "macros"] }

# Redis 客户端
redis = { version = "0.23", features = ["tokio-comp"] }

# 序列化
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# 异步运行时
tokio = { version = "1.0", features = ["full"] }

# 环境变量
dotenv = "0.15"

# 日志
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }

# 错误处理
anyhow = "1.0"
thiserror = "1.0"

# HTTP 客户端
reqwest = { version = "0.11", features = ["json"] }

# 时间处理
chrono = { version = "0.4", features = ["serde"] }

# UUID
uuid = { version = "1.0", features = ["v4", "serde"] }

# 异步工具
futures = "0.3"

# URL 编码
urlencoding = "2.1"

# OpenSSL 静态链接
openssl-sys = { version = "0.9", features = ["vendored"] }

[dev-dependencies]
tokio-test = "0.4"
