mod config;
mod models;
mod services;
mod handlers;
mod utils;

use actix_web::{web, App, HttpServer, middleware};
use actix_cors::Cors;
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

#[actix_web::main]
async fn main() -> std::io::Result<()> {
    // 首先加载环境变量
    dotenv::from_filename("config.env").ok();

    // 初始化日志
    tracing_subscriber::registry()
        .with(tracing_subscriber::EnvFilter::new(
            std::env::var("RUST_LOG").unwrap_or_else(|_| "info".into()),
        ))
        .with(tracing_subscriber::fmt::layer())
        .init();

    tracing::info!("🚀 启动 Rust API 服务...");

    // 加载配置
    let config = config::Config::from_env()
        .expect("加载配置失败");

    // 显示配置信息
    tracing::info!("数据库连接: {}", config.database_url);
    tracing::info!("Redis连接: {}", config.redis_url);
    tracing::info!("服务器地址: {}:{}", config.server_host, config.server_port);

    // 初始化数据库连接
    let db = utils::init_database(&config)
        .await
        .expect("数据库连接失败");

    // 初始化 Redis 连接
    let redis = utils::init_redis(&config)
        .expect("Redis 连接失败");

    // 测试连接
    utils::test_database_connection(&db)
        .await
        .expect("数据库连接测试失败");

    utils::test_redis_connection(&redis)
        .await
        .expect("Redis 连接测试失败");

    // 创建服务实例
    let question_service = web::Data::new(services::QuestionService::new(db, redis));

    // 启动 HTTP 服务器
    HttpServer::new(move || {
        App::new()
            .wrap(middleware::Logger::default())
            .wrap(
                Cors::default()
                    .allow_any_origin()
                    .allow_any_method()
                    .allow_any_header()
            )
            .app_data(question_service.clone())
            .service(
                web::scope("")
                    .route("/fbtimu", web::get().to(handlers::get_questions))
                    .route("/fbgethtimu", web::get().to(handlers::get_exam_questions))
                    .route("/gettimurank", web::get().to(handlers::get_question_rank))
                    .route("/fbremeber", web::post().to(handlers::save_page_state))
                    .route("/fbrecoverpage", web::get().to(handlers::recover_page_state))
                    .route("/fbpl", web::get().to(handlers::get_question_comments))
            )
            .route("/health", web::get().to(handlers::health_check))
    })
    .bind(format!("{}:{}", config.server_host, config.server_port))?
    .run()
    .await
}
